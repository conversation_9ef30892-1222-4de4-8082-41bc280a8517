{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "bunx biome lint --write && bunx tsc --noEmit", "format": "bunx biome format --write", "test": "vitest", "test:ui": "vitest --ui", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:seed": "prisma db seed", "import-csv": "tsx src/scripts/import-csv.ts", "import-excel": "tsx src/scripts/import-excel.ts", "data-manager": "tsx src/scripts/data-manager.ts", "migrate-hash": "tsx src/scripts/fix-duplicate-business-keys.ts", "index:es": "tsx src/scripts/index-to-elastic.ts", "setup-env": "node scripts/setup-env.js", "health-check": "curl -s http://localhost:3000/api/health | jq .", "analyze-traffic": "tsx src/scripts/analyze-traffic.ts", "cleanup-logs": "tsx src/scripts/cleanup-logs.ts", "category-order": "tsx scripts/category-order-manager.ts", "db-helper": "tsx scripts/database-helper.ts", "migrate-preview": "tsx scripts/migrate-database-separation.ts", "migrate-execute": "tsx scripts/migrate-database-separation.ts --execute", "migrate-validate": "tsx scripts/migrate-database-separation.ts --execute --validate", "migrate-cleanup": "tsx scripts/migrate-database-separation.ts --cleanup", "migrate-cleanup-force": "tsx scripts/migrate-database-separation.ts --cleanup-force", "import-csv-refactored": "tsx scripts/import-csv-refactored.ts", "test-refactored-system": "tsx src/scripts/test-refactored-system.ts", "test-refactored-apis": "curl -s http://localhost:3000/api/data/deviceCNImported/route-refactored | jq .", "compare-performance": "echo 'Performance comparison between old and new APIs'", "setup-refactored-schema": "cp prisma/schema-refactored.prisma prisma/schema.prisma && npx prisma generate"}, "prisma": {"seed": "tsx src/db/seed.ts"}, "dependencies": {"@elastic/elasticsearch": "^8.14.0", "@napi-rs/canvas": "^0.1.74", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.20", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "csv-parser": "^3.2.0", "ioredis": "^5.6.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.517.0", "next": "^15.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "redis": "^5.5.6", "sonner": "^2.0.7", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3.3.1", "@prisma/client": "^6.10.1", "@testing-library/react": "^16.3.0", "@types/bcrypt": "^5.0.2", "@types/node": "^20.19.1", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.6.0", "@vitest/ui": "^3.2.4", "bippy": "^0.3.16", "dotenv": "^16.5.0", "eslint": "^9.27.0", "eslint-config-next": "15.1.7", "ignore-loader": "^0.1.2", "jsdom": "^26.1.0", "postcss": "^8.5.3", "prisma": "^6.10.1", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}